/* 页面基础样式 */
.page {
	width: 100%;
	min-height: 100vh;
	background-color: #ffffff;
}

.main-content {
	max-width: 1920px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
}

/* 导航样式已移至 ../component/navigation/navigation.css */

/* 轮播图样式已移至 ../component/carousel/carousel.css */

/* 产品预览区域 */
.product-preview-section {
	background: linear-gradient(271deg, #f8f9fa 0%, #e9ecef 100%);
	padding: 80px 0;
	margin-top: 60px;
	width: 100%;
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.section-header {
	text-align: center;
	margin-bottom: 60px;
}

.section-title {
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #000000;
	line-height: 1.4;
	margin: 0;
	font-weight: 600;
}

.product-grid {
	display: flex;
	flex-direction: column;
	gap: 40px;
}

.product-row {
	display: grid;
	grid-template-columns: 2fr 1fr 2fr 1fr;
	gap: 30px;
	align-items: start;
}

.product-card {
	background-color: #f4f4f4;
	border-radius: 12px;
	padding: 28px 20px;
	height: 251px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	position: relative;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.product-card.featured-product {
	background-color: #f4f4f4;
}

.product-card:nth-child(3),
.product-card:nth-child(7) {
	background-color: rgba(216, 216, 216, 0.3);
}

.product-content {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;
}

.product-info {
	flex: 1;
}

.product-category {
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #000000;
	line-height: 1.3;
	margin: 0 0 12px 0;
	font-weight: 600;
}

.product-description {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #333333;
	line-height: 1.5;
	margin: 0;
}

.product-action {
	margin-top: 20px;
}

.learn-more-btn {
	display: inline-block;
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #000000;
	text-decoration: none;
	padding: 8px 18px;
	border: 1px solid #000000;
	border-radius: 4px;
	transition: all 0.3s ease;
	background-color: transparent;
}

.learn-more-btn:hover {
	background-color: #000000;
	color: #ffffff;
	transform: translateY(-1px);
}

.product-image {
	position: absolute;
	bottom: 20px;
	right: 20px;
	width: 94px;
	height: auto;
	object-fit: contain;
}

.product-thumbnail-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	padding: 10px;
}

.thumbnail-image {
	width: 20px;
	height: 20px;
	object-fit: contain;
	transition: transform 0.3s ease;
}

.thumbnail-image:hover {
	transform: scale(1.1);
}

.thumbnail-label {
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #000000;
	line-height: 1.4;
	text-align: center;
}

/* 响应式设计 - 必须包含的媒体查询 */
@media (max-width: 1200px) {
	.container {
		max-width: 960px;
		padding: 0 16px;
	}

	.product-row {
		grid-template-columns: 1fr 80px 1fr 80px;
		gap: 20px;
	}

	.section-title {
		font-size: 32px;
	}

	.product-card {
		padding: 24px 16px;
		height: 220px;
	}
}

@media (max-width: 900px) {
	.product-preview-section {
		padding: 60px 0;
	}

	.section-header {
		margin-bottom: 40px;
	}

	.section-title {
		font-size: 28px;
	}

	.product-row {
		grid-template-columns: 1fr;
		gap: 20px;
	}

	.product-card {
		height: auto;
		min-height: 180px;
		padding: 20px;
	}

	.product-image {
		position: static;
		width: 60px;
		margin-top: 16px;
		align-self: flex-end;
	}

	.product-thumbnail-card {
		flex-direction: row;
		justify-content: center;
		padding: 16px;
		background-color: rgba(248, 249, 250, 0.8);
		border-radius: 8px;
	}

	.thumbnail-image {
		width: 24px;
		height: 24px;
	}
}

@media (max-width: 600px) {
	.product-preview-section {
		padding: 40px 0;
		margin-top: 40px;
	}

	.container {
		padding: 0 12px;
	}

	.section-title {
		font-size: 24px;
	}

	.product-grid {
		gap: 24px;
	}

	.product-card {
		padding: 16px;
		min-height: 160px;
	}

	.product-category {
		font-size: 16px;
		margin-bottom: 8px;
	}

	.product-description {
		font-size: 13px;
		line-height: 1.4;
	}

	.learn-more-btn {
		font-size: 13px;
		padding: 6px 14px;
	}

	.product-image {
		width: 50px;
		margin-top: 12px;
	}

	.thumbnail-label {
		font-size: 14px;
	}
}

/* 公司介绍、解决方案、新闻区域样式已移至 ../component/home-content/home-content.css */

/* 联系我们样式已移至 ../component/contact/contact.css */

/* 页脚样式已移至 ../component/footer/footer.css */
